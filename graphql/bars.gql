query Bars($barsInput: BarsInput, $paginationInput: PaginationInput) {
  bars(barsInput: $barsInput, paginationInput: $paginationInput) {
    totalDocs
    limit
    page
    totalPages
    prevPage
    nextPage
    hasPrevPage
    hasNextPage
    pagingCounter
    docs {
      _id
      id
      createdAt
      updatedAt
      slug
      name
      description
      status
      logo
      coverImage
      images
      featured
      rating
      address {
        address
      }
      categories {
        name
        id
      }
      city {
        id
        name
      }
      businessHours {
        schedule {
          day
          timings
        }
      }
      happyHours {
        schedule {
          day
          timings
        }
      }
      contact {
        countryCode
        phone
      }
      menu {
        currency
        sections {
          name
          items {
            name
            price
            available
          }
        }
      }
    }
  }
}

query BarCategories {
  barCategories {
    totalDocs
    limit
    page
    totalPages
    prevPage
    nextPage
    hasPrevPage
    hasNextPage
    pagingCounter
    docs {
      _id
      id
      createdAt
      updatedAt
      name
      description
      image
      icon
    }
  }
}

query BarCategoryByName($name: String) {
  barCategory(id: null, name: $name) {
    _id
    id
    createdAt
    updatedAt
    name
    description
    image
    icon
  }
}
