query Neighborhoods(
  $neighborhoodsInput: NeighborhoodsInput
  $paginationInput: PaginationInput
) {
  neighborhoods(
    neighborhoodsInput: $neighborhoodsInput
    paginationInput: $paginationInput
  ) {
    totalDocs
    limit
    page
    totalPages
    prevPage
    nextPage
    hasPrevPage
    hasNextPage
    pagingCounter
    docs {
      _id
      id
      createdAt
      updatedAt
      slug
      name
      coverImage
      images
      city {
        _id
        id
        createdAt
        updatedAt
        name
        image
        coverImage
        heading
        subHeading
      }
    }
  }
}
