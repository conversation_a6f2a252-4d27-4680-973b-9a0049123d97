query Clubs($clubsInput: ClubsInput!, $paginationInput: PaginationInput) {
  clubs(clubsInput: $clubsInput, paginationInput: $paginationInput) {
    totalDocs
    limit
    page
    totalPages
    prevPage
    nextPage
    hasPrevPage
    hasNextPage
    pagingCounter
    docs {
      _id
      id
      createdAt
      updatedAt
      slug
      name
      description
      status
      logo
      coverImage
      images
      featured
      rating
      address {
        address
      }
      categories {
        name
        id
      }
      city {
        id
        name
      }
      businessHours {
        schedule {
          day
          timings
        }
      }
      contact {
        countryCode
        phone
      }
      menu {
        currency
        sections {
          name
          items {
            name
            price
            available
          }
        }
      }
    }
  }
}
