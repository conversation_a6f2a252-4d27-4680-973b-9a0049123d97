import { fetchData } from "@/client";
import LandingPage from "@/elements/landing-page";
import {
  BarsDocument,
  BarsQuery,
  BarsQueryVariables,
  CitiesDocument,
  CitiesQuery,
  CitiesQueryVariables,
  ClubsDocument,
  ClubsQuery,
  ClubsQueryVariables,
  NeighborhoodsDocument,
  NeighborhoodsQuery,
  NeighborhoodsQueryVariables,
} from "@/generated/graphql";

export const metadata = {
  title: "Seeker - Explore Bars and Clubs",
  description:
    "Seeker is a platform that connects users with bars and clubs, offering a seamless way to explore venues across cities.",
};

export default async function LandingPageScreen() {
  const cityRes = await fetchData<CitiesQuery, CitiesQueryVariables>(
    CitiesDocument,
    { citiesInput: { name: "Paris" } }
  )();

  const city = cityRes?.cities?.docs?.[0];
  if (!city) {
    throw new Error("City data not found");
  }

  const neighborhoodsRes = await fetchData<
    NeighborhoodsQuery,
    NeighborhoodsQueryVariables
  >(NeighborhoodsDocument, {
    neighborhoodsInput: { city: city._id },
  })();

  const barsRes = await fetchData<BarsQuery, BarsQueryVariables>(BarsDocument, {
    barsInput: { city: city._id },
  })();

  const clubsRes = await fetchData<ClubsQuery, ClubsQueryVariables>(
    ClubsDocument,
    { clubsInput: { city: city._id } }
  )();

  const neighborhoods = neighborhoodsRes?.neighborhoods;
  const bars = barsRes?.bars;
  const clubs = clubsRes?.clubs;

  return (
    <div>
      {city?.coverImage ? <LandingPage.HeroSection {...city} /> : null}
      <LandingPage.PopularSections
        neighborhoods={neighborhoods?.docs}
        bars={bars?.docs}
        clubs={clubs?.docs}
      />
      <LandingPage.CallToDownloadSection />
    </div>
  );
}
