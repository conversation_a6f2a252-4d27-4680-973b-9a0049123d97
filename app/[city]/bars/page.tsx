import { fetchData } from "@/client";
import PopularCard from "@/components/cards/popular.card";
import AppButton from "@/components/common/AppButton";
import BarsHomePage from "@/elements/bars-homepage";
import {
  BarCategoriesDocument,
  BarCategoriesQuery,
  BarCategoriesQueryVariables,
  BarCategoryByNameDocument,
  BarCategoryByNameQuery,
  BarCategoryByNameQueryVariables,
  BarsDocument,
  BarsQuery,
  BarsQueryVariables,
  CitiesDocument,
  CitiesQuery,
  CitiesQueryVariables,
} from "@/generated/graphql";
import { Metadata } from "next";
import Link from "next/link";

export const generateMetadata = async (props: {
  params: Promise<{ city: string }>;
}): Promise<Metadata> => {
  const { city: cityNameParam } = await props.params;

  return {
    title: `Seeker – Bars in ${cityNameParam}`,
    description: `Explore the best bars in ${cityNameParam} with Seeker. Discover top-rated venues, happy hours, and more.`,
    openGraph: {
      title: `Seeker – Bars in ${cityNameParam}`,
      description: `Explore the best bars in ${cityNameParam} with Seeker. Discover top-rated venues, happy hours, and more.`,
      url: `https://seeker.com/${cityNameParam}/bars`,
      images: [
        {
          url: `https://seeker.com/images/bars-${cityNameParam}.jpg`,
          width: 1200,
          height: 630,
          alt: `Best bars in ${cityNameParam}`,
        },
      ],
    },
  };
};

export default async function Bars({
  params,
  searchParams,
}: {
  params: Promise<{ city: string }>;
  searchParams: Promise<{ [key: string]: string | undefined }>;
}) {
  const { category } = await searchParams;
  const { city: cityParam } = await params;

  const {
    cities: {
      docs: [city],
    },
  } = await fetchData<CitiesQuery, CitiesQueryVariables>(CitiesDocument, {
    citiesInput: { name: cityParam },
  })();

  const { barCategories } = await fetchData<
    BarCategoriesQuery,
    BarCategoriesQueryVariables
  >(BarCategoriesDocument)();

  let categoryRes: BarCategoryByNameQuery | undefined;

  if (category?.length) {
    const res = await fetchData<
      BarCategoryByNameQuery,
      BarCategoryByNameQueryVariables
    >(BarCategoryByNameDocument, { name: category })();
    categoryRes = res;
  }
  const { bars: barsWithFilter } = await fetchData<
    BarsQuery,
    BarsQueryVariables
  >(BarsDocument, {
    barsInput: {
      city: city._id,
      categories: categoryRes?.barCategory?.id
        ? [categoryRes.barCategory.id]
        : undefined,
    },
  })();

  return (
    <>
      <BarsHomePage.HeroSection />
      {category ? (
        <section>
          <div className="flex flex-col items-center justify-center sm:grid sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 lg:gap-9 container my-[65px] lg:my-[76px]">
            {barsWithFilter?.docs?.map((bar) => (
              <PopularCard cardType="bar" data={bar} key={bar.id} />
            ))}
          </div>
          <div className="flex flex-col gap-[50px] lg:gap-[28px] container">
            <div className="font-bold text-2xl leading-5 text-secondary">
              Explore Other Bars
            </div>
            <div className="flex flex-col w-full sm:flex-row sm:items-center gap-5 mb-[72px]">
              <Link
                href={`/${city.name}/bars`}
                className="px-[28px] rounded-none sm:px-[28px] py-[11px]  lg:text-xl font-bold leading-5 bg-primary text-white"
              >
                All Bars
              </Link>
              {barCategories?.docs?.map((category) => (
                <Link
                  key={category.id}
                  href={{ query: { category: category.name } }}
                  className="px-[28px] rounded-none sm:px-[28px] py-[11px]  lg:text-xl font-bold leading-5 bg-primary text-white"
                >
                  {category.name}
                </Link>
              ))}
            </div>
          </div>
        </section>
      ) : (
        <>
          <BarsHomePage.BestBarsSection />
          <BarsHomePage.BarsSection cityId={city.id} />
          <BarsHomePage.CallToDownloadSection />
        </>
      )}
      <BarsHomePage.FAQSection />
    </>
  );
}
