import { fetchData } from "@/client";
import ClubsHomePage from "@/elements/clubs-homepage";
import {
  CitiesDocument,
  CitiesQuery,
  CitiesQueryVariables,
  ClubsDocument,
  ClubsQuery,
  ClubsQueryVariables,
} from "@/generated/graphql";
import { Metadata } from "next";
import React from "react";

export const generateMetadata = async (props: {
  params: Promise<{ city: string }>;
}): Promise<Metadata> => {
  const { city: cityNameParam } = await props.params;

  return {
    title: `Seeker – Clubs in ${cityNameParam}`,
    description: `Explore the best clubs in ${cityNameParam} with Seeker. Discover top-rated venues and more.`,
    openGraph: {
      title: `Seeker – clubs in ${cityNameParam}`,
      description: `Explore the best clubs in ${cityNameParam} with Seeker. Discover top-rated venues and more.`,
      url: `https://seeker.com/${cityNameParam}/clubs`,
      images: [
        {
          url: `https://seeker.com/images/clubs-${cityNameParam}.jpg`,
          width: 1200,
          height: 630,
          alt: `Best clubs in ${cityNameParam}`,
        },
      ],
    },
  };
};

export default async function Clubs({
  params,
}: {
  params: Promise<{ city: string }>;
}) {
  const { city: cityParam } = await params;

  const {
    cities: {
      docs: [city],
    },
  } = await fetchData<CitiesQuery, CitiesQueryVariables>(CitiesDocument, {
    citiesInput: { name: cityParam },
  })();

  const { clubs } = await fetchData<ClubsQuery, ClubsQueryVariables>(
    ClubsDocument,
    { clubsInput: { city: city._id } }
  )();

  return (
    <>
      <ClubsHomePage.HeroSection />
      <ClubsHomePage.BestClubsSection />
      <ClubsHomePage.ClubsSection clubs={clubs?.docs} />
      <ClubsHomePage.CallToDownloadSection />
      <ClubsHomePage.FAQSection />
    </>
  );
}
