@import "tailwindcss";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-backgroundlight: var(--backgroundlight);
  --color-foreground: var(--foreground);
  --color-footerbg: var(--footerbg);
  --color-inactive: var(--inactive);
  --color-textgreen: var(--textgreen);
  --color-success: var(--success);
  --color-failed: var(--failed);
  --color-text: var(--text);
  --font-montserrat: var(--font-montserrat);
  --font-montserrat-alternates: var(--font-montserrat-alternates);
  --color-muted-primary: var(--muted-primary);
  --color-secondary-dark: var(--secondary-dark);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: hsla(0, 0%, 100%, 1);
  --backgroundlight: hsla(38, 100%, 97%, 1);
  --foreground: hsla(0, 0%, 13%, 1);
  --inactive: hsla(0, 0%, 76%, 1);
  --footerbg: hsla(38, 57%, 89%, 1);
  --text: hsla(0, 0%, 13%, 1);
  --textgreen: hsla(164, 12%, 45%, 1);
  --success: hsla(143, 85%, 31%, 1);
  --failed: hsla(349, 89%, 55%, 1);
  --card: oklch(1 0 0);
  --card-foreground: hsla(0, 0%, 13%, 1);
  --popover: oklch(1 0 0);
  --popover-foreground: hsla(0, 0%, 13%, 1);
  --primary: hsla(20, 92%, 61%, 1);
  --primary-foreground: hsla(0, 0%, 13%, 1);
  --secondary: hsla(202, 55%, 25%, 1);
  --secondary-dark: hsla(164, 12%, 45%, 1);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: hsla(202, 55%, 25%, 1);
  --muted-foreground: 0 0% 33%;
  --muted-primary: hsla(40, 86%, 66%, 1);
  --accent: hsla(202, 55%, 25%, 1);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: 0 0% 0%;
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: 0 0% 0%;
  --sidebar-accent: hsla(202, 55%, 25%, 1);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: hsla(0, 0%, 100%, 1);
  --backgroundlight: hsla(38, 100%, 97%, 1);
  --foreground: 0 0% 100%;
  --inactive: 0 0% 76%;
  --footerbg: 38 57% 89%;
  --text: hsla(0, 0%, 13%, 1);
  --textgreen: hsla(164, 12%, 45%, 1);
  --success: hsla(143, 85%, 31%, 1);
  --failed: hsla(349, 89%, 55%, 1);
  --card: oklch(0.205 0 0);
  --card-foreground: 0 0% 100%;
  --popover: oklch(0.205 0 0);
  --popover-foreground: 0 0% 100%;
  --primary: hsla(20, 92%, 61%, 1);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: hsla(164, 12%, 45%, 1);
  --secondary-dark: hsla(202, 55%, 25%, 1);
  --secondary-foreground: 0 0% 100%;
  --muted: 202 55% 25%;
  --muted-primary: hsla(40, 86%, 66%, 1);
  --muted-foreground: 0 0% 83%;
  --accent: 202 55% 25%;
  --accent-foreground: 0 0% 100%;
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: 0 0% 100%;
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 202 55% 25%;
  --sidebar-accent-foreground: 0 0% 100%;
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@utility container {
  margin: 0 auto;
  padding-inline: 1.5rem;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .bars-banner-gradient {
    background-image: linear-gradient(
      to right,
      var(--color-muted-primary) 50%,
      hsla(20, 92%, 61%, 0.7)
    );
  }
  .city-banner-gradient {
    background-image: linear-gradient(
      to right,
      var(--color-secondary),
      var(--color-muted-primary) 65%,
      var(--color-primary)
    );
  }
  .clubs-banner-gradient {
    background-image: linear-gradient(
      to bottom right,
      var(--color-text) 55%,
      var(--color-secondary)
    );
  }
  .contact-banner-gradient {
    background-image: linear-gradient(
      to right,
      var(--color-primary),
      var(--color-muted-primary),
      var(--color-secondary-dark),
      var(--color-secondary)
    );
  }
  .contact-form-gradient {
    background-image: linear-gradient(
      to bottom right,
      hsla(20, 92%, 61%, 0.4),
      hsla(40, 86%, 66%, 0.4),
      hsla(164, 12%, 45%, 0.4),
      hsla(202, 55%, 25%, 0.4)
    );
  }
  .bars-section-background-gradient {
    background-image: linear-gradient(
      to bottom,
      var(--color-footerbg) 10%,
      var(--color-white) 80%
    );
  }
  .btn-gradient-4 {
    background-image: linear-gradient(
      to right,
      var(--color-primary) 10%,
      var(--color-muted-primary),
      #658079,
      var(--color-secondary)
    );
  }
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }
  .hide-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
  .scrollbar-theme {
    scrollbar-width: thin;
    scrollbar-color: var(--color-muted-primary) transparent;
  }
  .shimmer {
    position: relative;
    overflow: hidden;
    background-color: #e5e7eb; /* Tailwind's gray-200 */
  }

  .shimmer::after {
    content: "";
    position: absolute;
    top: 0;
    left: -150px;
    height: 100%;
    width: 150px;
    background: linear-gradient(
      to right,
      transparent 0%,
      rgba(255, 255, 255, 0.4) 50%,
      transparent 100%
    );
    animation: shimmer 1.5s infinite;
  }
}
@keyframes shimmer {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(100%);
  }
}
