"use client";

import { isAxiosError } from "axios";
import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import { toast } from "sonner";
import { setHours, setMinutes, setSeconds } from "date-fns";
import { useCallback, useState } from "react";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Converts a number like 1700 into a Date object set to 5:00 PM.
 *
 * @param time - Time in 24-hour format as a number (e.g. 930, 1700, 1830)
 * @param baseDate - Optional base Date (defaults to today)
 * @returns Date object with the specified time
 */
export function parseTimeToDate(
  time: number,
  baseDate: Date = new Date()
): Date {
  const hours = Math.floor(time / 100);
  const minutes = time % 100;
  return setSeconds(setMinutes(setHours(baseDate, hours), minutes), 0);
}

export function errorHandler(ex: unknown) {
  let errorMessage = "Something went wrong";
  if (isAxiosError(ex)) {
    errorMessage = ex.response?.data?.message || ex.message || "Error…";
  } else if (ex instanceof Error) {
    errorMessage = ex.message;
  }

  toast(errorMessage, {
    description: "Please try again.",
    icon: "❌",
    duration: 4000,
    style: { backgroundColor: "#f87171", color: "#fff" }, // optional error styling
  });
}

export function useHandleToggleAccordion(index: number | string) {
  const [activeIndex, setActiveIndex] = useState<number | string>(0);
  const handleToggle = useCallback(
    (index: number | string) => () => {
      setActiveIndex((prev) => (prev === index ? "" : index));
    },
    []
  );
  return { activeIndex, handleToggle };
}
