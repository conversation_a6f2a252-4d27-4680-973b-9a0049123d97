"use client";

import CityCard from "@/components/cards/city.card";
import PopularCard from "@/components/cards/popular.card";
import HorizontalScrollCards from "@/components/common/HorizontalScrollCards";
import { BarsQuery, ClubsQuery, NeighborhoodsQuery } from "@/generated/graphql";
import { cn } from "@/lib/utils";
import Image from "next/image";
import { useRouter } from "next/navigation";

type PopularSectionProps = {
  neighborhoods: NeighborhoodsQuery["neighborhoods"]["docs"];
  bars: BarsQuery["bars"]["docs"];
  clubs: ClubsQuery["clubs"]["docs"];
};

const SkeletonCityCard = () => (
  <div className="w-[310px] min-w-[310px] rounded-2xl bg-white p-[14px] flex flex-col shadow-lg shadow-neutral-300/20 border border-neutral-200/30">
    {/* Image Skeleton */}
    <div className="w-full h-[173px] rounded-xl shimmer" />

    {/* Title and subtitle */}
    <div className="flex items-center gap-2.5 mt-[23px] mb-[26px]">
      <div className="w-1/2 h-4 rounded shimmer" />
      <div className="w-1 h-4 rounded shimmer" />
      <div className="w-1/4 h-4 rounded shimmer" />
    </div>

    {/* Stats */}
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-2">
        <div className="w-5 h-5 rounded-full shimmer" />
        <div className="w-8 h-4 rounded shimmer" />
      </div>
      <div className="flex items-center gap-2">
        <div className="w-5 h-5 rounded-full shimmer" />
        <div className="w-8 h-4 rounded shimmer" />
      </div>
    </div>
  </div>
);

const SkeletonPopularCard = ({
  cardType = "bar",
}: {
  cardType: "bar" | "club";
}) => (
  <div className="w-[298px] min-w-[298px] rounded-2xl bg-white overflow-hidden shadow-md animate-pulse">
    {/* Image Placeholder */}
    <div className="w-full h-[198px] shimmer" />

    {/* Content Area */}
    <div
      className={cn(
        "bg-gradient-to-t from-orange-100 to-orange-50 px-4 py-3 flex flex-col gap-2",
        cardType === "club" &&
          "bg-gradient-to-r from-secondary/20 to-secondary/25"
      )}
    >
      {/* Logo + Title Row */}
      <div className="flex justify-between items-center">
        <div className="w-10 h-10 rounded-md shimmer" />
        <div className="w-12 h-4 shimmer rounded" />
      </div>

      {/* Name */}
      <div className="w-3/4 h-4 shimmer rounded" />

      {/* Location */}
      <div className="w-full h-4 shimmer rounded" />

      {/* Tags */}
      <div className="flex gap-2 mt-2">
        <div className="w-16 h-6 shimmer rounded-full" />
        <div className="w-20 h-6 shimmer rounded-full" />
      </div>
    </div>
  </div>
);

export default function PopularSections({
  neighborhoods,
  bars,
  clubs,
}: PopularSectionProps) {
  const router = useRouter();
  const neighborhoodsData = neighborhoods;
  const barsData = bars;
  const clubsData = clubs;

  return (
    <div className="w-full bg-background">
      {/* POPULAR CITIES */}
      <section className="w-full bg-background flex flex-col items-center container md:pt-[102px] pt-[43px] md:pb-[95px] pb-[38px]">
        <h2 className="font-bold text-xl mb-[59.5px] md:mb-[67px] md:text-4xl lg:text-[40px]">
          Popular Cities
        </h2>
        <HorizontalScrollCards>
          {neighborhoodsData?.map((city) => (
            <CityCard
              data={city}
              key={city.id}
              onClick={() => router.push(`/${city.city.name}/bars`)}
            />
          ))}
        </HorizontalScrollCards>
      </section>

      {/* CHEERS SECTION */}
      <section className="w-full bg-gradient-to-br lg:bg-gradient-to-r from-primary  via-40% via-muted-primary to-90% to-secondary mt-[73.5px] md:mt-[95px]">
        <div className="w-full flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4 py-[52px] px-4 lg:py-16 container">
          <p className="font-medium text-[32px] md:text-[38px] xl:text-[48px] leading-[39px] md:leading-[50px] text-white md:max-w-[359px] lg:max-w-[450px] xl:max-w-[556px] xl:leading-[61px]">
            Find bars, clubs, student hotspots, and VIP venues tailored to your
            vibe.
          </p>
          <Image
            src={"/svg/cheers-white.svg"}
            width={320}
            height={117}
            alt="cheers"
            className="lg:hidden mt-4 self-start md:self-end w-[320px] h-[117px]"
          />
          <Image
            src={"/svg/cheers.svg"}
            width={380}
            height={128}
            alt="cheers"
            className="hidden w-[380px] h-[128px] lg:block "
          />
        </div>
      </section>

      {/* POPULAR BARS */}
      <section className="w-full bg-background flex flex-col items-center container md:mt-[111px] mt-[43px]">
        <h2 className="font-bold text-xl mb-[48px] md:mb-[75px] md:text-4xl lg:text-[40px]">
          Popular Bars
        </h2>
        <div className="flex flex-col gap-[47px] sm:gap-0 w-full">
          <HorizontalScrollCards>
            {barsData?.map((card) => (
              <PopularCard
                cardType="bar"
                data={card}
                key={card._id}
                onClick={() =>
                  router.push(`/${card.city.name}/bars/${card.slug}`)
                }
              />
            ))}
          </HorizontalScrollCards>
          <div className="lg:hidden w-full">
            <HorizontalScrollCards>
              {barsData?.reverse().map((card) => (
                <PopularCard
                  cardType="bar"
                  data={card}
                  key={card._id}
                  onClick={() =>
                    router.push(`/${card.city.name}/bars/${card.slug}`)
                  }
                />
              ))}
            </HorizontalScrollCards>
          </div>
        </div>
      </section>

      {/* SOCIAL GATHERING  */}
      <section className="mt-[59px] lg:mt-[94px]">
        <div className="w-full h-[465px] lg:h-[594px] relative">
          <Image
            src={"/images/socialgathering.webp"}
            alt="socialgathering"
            fill
            className="hidden lg:block object-cover"
            loading="eager"
            sizes="(max-width: 1024px) 100vw, 100vw"
          />
          <Image
            src={"/images/socialgathering_mobile.webp"}
            alt="socialgathering"
            fill
            className="lg:hidden object-cover"
            loading="eager"
            sizes="(max-width: 768px) 100vw"
          />
          <Image
            src={"/svg/papercuts.svg"}
            width={407}
            height={53}
            alt="papercuts"
            className="hidden sm:block absolute bottom-0 w-full h-auto lg:w-full lg:h-auto object-contain"
          />
          <Image
            src={"/svg/papercuts_mobile.svg"}
            width={407}
            height={53}
            alt="papercuts"
            className="absolute bottom-0 w-full h-auto sm:hidden object-contain"
          />
        </div>
      </section>

      {/* POPULAR CLUBS */}
      <section className="w-full bg-background flex flex-col items-center container md:mt-[102px] mt-[72px] ">
        <h2 className="font-bold text-xl mb-[47px] md:mb-[88px] md:text-4xl lg:text-[40px]">
          Popular Clubs
        </h2>
        <div className="flex flex-col gap-[47px] sm:gap-0 w-full mb-[55px] md:mb-[75px]">
          <HorizontalScrollCards>
            {clubsData?.map((card) => (
              <PopularCard
                cardType="club"
                data={card}
                key={card._id}
                onClick={() =>
                  router.push(`/${card.city.name}/clubs/${card.slug}`)
                }
              />
            ))}
          </HorizontalScrollCards>
          <div className="lg:hidden w-full">
            <HorizontalScrollCards>
              {clubsData?.reverse().map((card) => (
                <PopularCard
                  cardType="club"
                  data={card}
                  key={card._id}
                  onClick={() =>
                    router.push(`/${card.city.name}/clubs/${card.slug}`)
                  }
                />
              ))}
            </HorizontalScrollCards>
          </div>
        </div>
      </section>
    </div>
  );
}
