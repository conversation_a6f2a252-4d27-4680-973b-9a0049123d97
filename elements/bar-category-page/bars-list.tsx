import React from "react";

export default function BarsList() {
  return (
    <section>
      <div className="flex flex-col items-center justify-center sm:grid sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 lg:gap-9 container my-[65px] lg:my-[76px]">
        {bars?.docs?.map((bar) => (
          <PopularCard cardType="bar" data={bar} key={bar.id} />
        ))}
      </div>
      <div className="flex flex-col gap-[50px] lg:gap-[28px] container">
        <div className="font-bold text-2xl leading-5 text-secondary">
          Explore Other Bars
        </div>
        <div className="flex flex-col w-full sm:flex-row sm:items-center gap-5 mb-[72px]">
          {barCategories?.docs?.map((category) => (
            <AppButton
              key={category.id}
              variant="default"
              className="px-[28px] rounded-none sm:px-[28px] py-[11px]  lg:text-xl font-bold leading-5"
              onClick={handleCategoryClick(category.id)}
            >
              {category.name}
            </AppButton>
          ))}
        </div>
      </div>
    </section>
  );
}
