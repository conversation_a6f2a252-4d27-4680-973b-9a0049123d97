import PopularCard from "@/components/cards/popular.card";
import AppButton from "@/components/common/AppButton";
import HorizontalScrollCards from "@/components/common/HorizontalScrollCards";
import TempData from "@/data";
import React from "react";

export default function ExploreNearbyBars() {
  const { barCardsData } = TempData();
  return (
    <section className="w-full flex flex-col items-center container mb-[120px] xl:mb-[88px]">
      <h2 className="text-xl lg:text-3xl xl:text-[40px] font-bold text-text mt-3 xl:mt-4 text-center">
        Explore Nearby Bars
      </h2>
      <div className="mt-[42px] w-full flex flex-col items-center justify-center ">
        <HorizontalScrollCards>
          {barCardsData.map((card, idx) => (
            <PopularCard {...card} key={idx} />
          ))}
        </HorizontalScrollCards>
        <div className="lg:hidden w-full">
          <HorizontalScrollCards>
            {barCardsData.reverse().map((card, idx) => (
              <PopularCard {...card} key={idx} />
            ))}
          </HorizontalScrollCards>
        </div>
        <AppButton className="hidden lg:flex lg:px-7 lg:py-[11px] lg:font-semibold lg:mt-8">
          View all
        </AppButton>
      </div>
    </section>
  );
}
