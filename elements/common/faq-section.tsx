"use client";

import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
} from "@/components/common/Accordion";
import COLORS from "@/constants/colors";
import { useHandleToggleAccordion } from "@/lib/utils";
import ICONS from "@/public/icons";
import { useCallback, useState } from "react";

const accordianData = [
  {
    id: 1,
    question: "Lorem Ipsum is simply dummy text",
    answer:
      "Lorem Ipsum is simply dummy textLorem Ipsum is simply dummy textLorem Ipsum is simply dummy textLorem Ipsum is simply dummy text",
  },
  {
    id: 2,
    question: "Lorem Ipsum is simply dummy text",
    answer:
      "Lorem Ipsum is simply dummy textLorem Ipsum is simply dummy textLorem Ipsum is simply dummy textLorem Ipsum is simply dummy text",
  },
  {
    id: 3,
    question: "Lorem Ipsum is simply dummy text",
    answer:
      "Lorem Ipsum is simply dummy textLorem Ipsum is simply dummy textLorem Ipsum is simply dummy textLorem Ipsum is simply dummy text",
  },
  {
    id: 4,
    question: "Lorem Ipsum is simply dummy text",
    answer:
      "Lorem Ipsum is simply dummy textLorem Ipsum is simply dummy textLorem Ipsum is simply dummy textLorem Ipsum is simply dummy text",
  },
  {
    id: 5,
    question: "Lorem Ipsum is simply dummy text",
    answer:
      "Lorem Ipsum is simply dummy textLorem Ipsum is simply dummy textLorem Ipsum is simply dummy textLorem Ipsum is simply dummy text",
  },
  {
    id: 6,
    question: "Lorem Ipsum is simply dummy text",
    answer:
      "Lorem Ipsum is simply dummy textLorem Ipsum is simply dummy textLorem Ipsum is simply dummy textLorem Ipsum is simply dummy text",
  },
];

export default function FAQSection() {
  const { activeIndex, handleToggle } = useHandleToggleAccordion(0);

  return (
    <section className="w-full h-[600px] flex flex-col items-center container pb-[120px] xl:pb-[88px]">
      <h2 className="text-xl lg:text-3xl xl:text-[40px] font-bold text-text mt-3 xl:mt-4 text-center">
        Frequently Asked Questions
      </h2>
      <div className="mt-[42px] w-full max-w-[1079px] flex flex-col gap-2 h-full overflow-y-auto max-h-[650px] scrollbar-theme">
        {accordianData.map((accordian, index) => (
          <Accordion key={accordian.id}>
            <AccordionTrigger
              onClick={handleToggle(index)}
              isActive={activeIndex === index}
              className="text-textgreen py-7"
            >
              {activeIndex === index ? (
                <ICONS.MinusIcon
                  width={14}
                  height={14}
                  color={COLORS.LIGHT.PRIMARY}
                  className="w-[14px] h-[14px] mr-2 xl:w-[18px] xl:h-[18px]"
                />
              ) : (
                <ICONS.PlusIcon
                  width={14}
                  height={14}
                  color={COLORS.LIGHT.PRIMARY}
                  className="w-[14px] h-[14px] mr-2 xl:w-[18px] xl:h-[18px]"
                />
              )}
              {accordian.question}
            </AccordionTrigger>
            <AccordionItem isActive={activeIndex === index}>
              <p className="text-text font-semibold text-sm lg:text-base xl:max-w-[80%] pl-8 py-4">
                {accordian.answer}
              </p>
            </AccordionItem>
          </Accordion>
        ))}
      </div>
    </section>
  );
}
