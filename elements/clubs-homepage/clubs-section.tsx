"use client";

import GridCard from "@/components/cards/grid.card";
import PopularCard from "@/components/cards/popular.card";
import AppButton from "@/components/common/AppButton";
import HorizontalScrollCards from "@/components/common/HorizontalScrollCards";
import TempData from "@/data";
import { ClubsQuery } from "@/generated/graphql";
import { useRouter } from "next/navigation";
import { useCallback } from "react";

type ClubsProps = {
  clubs: ClubsQuery["clubs"]["docs"];
};

export default function ClubsSection({ clubs }: ClubsProps) {
  const router = useRouter();
  const clubsData = clubs;
  const { tempData } = TempData();

  const handleCardClick = useCallback(
    (slug: string) => () => {
      router.push(`clubs/${slug}`);
    },
    [clubsData]
  );

  return (
    <div className="bars-section-background-gradient w-full pt-[45px] xl:pt-[98px]">
      <section className="w-full flex flex-col container mb-[45px] xl:mb-[88px]">
        <h2 className="text-xl lg:text-[40px] font-bold text-text  text-center mb-10.5 lg:mb-[73px]">
          Popular Clubs
        </h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-7 sm:gap-2 xl:gap-[38px]">
          {tempData.map((bar, idx) => (
            <div
              key={idx}
              className={`${
                tempData.length % 2 !== 0 && idx === tempData.length - 1
                  ? " md:col-span-2 md:w-1/2 md:mx-auto lg:col-span-3 lg:w-1/3 lg:mx-auto"
                  : ""
              }`}
            >
              <GridCard {...bar} />
            </div>
          ))}
        </div>
      </section>

      {/* STUDENT CLUBS */}
      <section className="w-full flex flex-col items-center container mb-[45px]">
        <h2 className="text-xl lg:text-[40px] font-bold text-text  text-center mb-10.5 lg:mb-[73px]">
          Student Clubs
        </h2>
        <div className="w-full flex flex-col items-center justify-center">
          <HorizontalScrollCards>
            {clubsData?.map((card, idx) => (
              <PopularCard
                data={card}
                key={card.id}
                cardType="club"
                onClick={handleCardClick(card?.slug)}
              />
            ))}
          </HorizontalScrollCards>
          <div className="lg:hidden w-full">
            <HorizontalScrollCards>
              {clubsData
                ?.slice()
                .reverse()
                .map((card, idx) => (
                  <PopularCard
                    data={card}
                    key={card.id}
                    cardType="club"
                    onClick={handleCardClick(card?.slug)}
                  />
                ))}
            </HorizontalScrollCards>
          </div>
          <AppButton className="hidden lg:flex lg:px-7 lg:py-[11px] lg:font-semibold lg:mt-[53px]">
            View all
          </AppButton>
        </div>
      </section>

      {/* AFFORDABLE CLUBS */}
      <section className="w-full flex flex-col items-center container mb-[45px]">
        <h2 className="text-xl lg:text-[40px] font-bold text-text  text-center mb-10.5">
          Affordable Clubs
        </h2>
        <div className="w-full flex flex-col items-center justify-center ">
          <HorizontalScrollCards>
            {clubsData?.map((card, idx) => (
              <PopularCard
                data={card}
                key={card.id}
                cardType="club"
                onClick={handleCardClick(card?.slug)}
              />
            ))}
          </HorizontalScrollCards>
          <div className="lg:hidden w-full">
            <HorizontalScrollCards>
              {clubsData
                ?.slice()
                .reverse()
                .map((card, idx) => (
                  <PopularCard
                    data={card}
                    key={card.id}
                    cardType="club"
                    onClick={handleCardClick(card?.slug)}
                  />
                ))}
            </HorizontalScrollCards>
          </div>
          <AppButton className="hidden lg:flex lg:px-7 lg:py-[11px] lg:font-semibold lg:mt-[53px]">
            View all
          </AppButton>
        </div>
      </section>

      {/* AFTERWORK CLUBS */}
      <section className="w-full flex flex-col items-center container mb-[120px] xl:mb-[88px]">
        <h2 className="text-xl lg:text-3xl xl:text-[40px] font-bold text-text mt-3 xl:mt-4 text-center">
          Afterwork Clubs
        </h2>
        <div className="mt-[42px]  w-full flex flex-col items-center justify-center ">
          <HorizontalScrollCards>
            {clubsData?.map((card, idx) => (
              <PopularCard
                data={card}
                key={card.id}
                cardType="club"
                onClick={handleCardClick(card?.slug)}
              />
            ))}
          </HorizontalScrollCards>
          <div className="lg:hidden w-full">
            <HorizontalScrollCards>
              {clubsData
                ?.slice()
                .reverse()
                .map((card, idx) => (
                  <PopularCard
                    data={card}
                    key={card.id}
                    cardType="club"
                    onClick={handleCardClick(card?.slug)}
                  />
                ))}
            </HorizontalScrollCards>
          </div>
          <AppButton className="hidden lg:flex lg:px-7 lg:py-[11px] lg:font-semibold lg:mt-8">
            View all
          </AppButton>
        </div>
      </section>
    </div>
  );
}
