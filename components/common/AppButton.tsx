"use client";

import React from "react";
import { cn } from "@/lib/utils";

type AppButtonProps = {
  children?: React.ReactNode;
  variant?:
    | "default"
    | "gradient2"
    | "outline"
    | "secondary"
    | "gradient4"
    | "light-gradient";
} & React.ButtonHTMLAttributes<HTMLButtonElement>;

const variantClasses: Record<NonNullable<AppButtonProps["variant"]>, string> = {
  default: "bg-primary",
  gradient2:
    "bg-gradient-to-br from-muted-primary via-primary to-primary font-semibold",
  outline:
    "border-2 border-primary bg-transparent backdrop-blur-sm font-montserrat font-bold",
  secondary:
    "bg-gradient-to-br from-text to-secondary font-semibold flex flex-1 items-center justify-center",
  gradient4: "btn-gradient-4 font-semibold",
  "light-gradient":
    "bg-gradient-to-br from-muted-primary from-70% to-primary/40 font-semibold",
};

export default function AppButton({
  children,
  variant = "default",
  onClick,
  className,
  ...props
}: AppButtonProps) {
  return (
    <button
      onClick={onClick}
      className={cn(
        "transition-all rounded-full hover:scale-101 duration-300 !cursor-pointer text-base text-white",
        variantClasses[variant],
        className
      )}
      {...props}
    >
      {children}
    </button>
  );
}
