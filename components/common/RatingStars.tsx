"use client";

import { cn } from "@/lib/utils";
import React from "react";
import { Rating } from "react-simple-star-rating";

export default function RatingStars({
  className,
  ratingValue = 4.5,
  width = 14,
  height = 13,
  textStyles,
  ...props
}: React.ComponentPropsWithoutRef<"div"> & {
  ratingValue?: number;
  width?: number;
  height?: number;
  textStyles?: string;
}) {
  return (
    <div
      className={cn(
        "bg-white border border-gray-200 rounded-full inline-flex items-center",
        className
      )}
      {...props}
    >
      <Rating
        initialValue={ratingValue}
        allowFraction={true}
        readonly={true}
        emptyIcon={
          <img
            src="/svg/empty-star.svg"
            width={width}
            height={height}
            alt="empty-star"
            style={{
              display: "inline-flex",
              alignItems: "center",
              marginLeft: 1,
              marginBottom: 7,
            }}
          />
        }
        fillIcon={
          <img
            src="/svg/fill-star.svg"
            width={width}
            height={height}
            alt="fill-star"
            style={{
              display: "inline-flex",
              alignItems: "center",
              marginLeft: 1,
              marginBottom: 7,
            }}
          />
        }
        allowHover={false}
      />
      <span
        className={cn(
          "text-muted-foreground text-[10px] font-montserrat-alternates font-medium ml-1 mb-[1px]",
          textStyles
        )}
      >
        ({ratingValue})
      </span>
    </div>
  );
}
