"use client";

import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import {
  Sheet,
  Sheet<PERSON>ontent,
  She<PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from "@/components/ui/sheet";
import { CitiesQuery } from "@/generated/graphql";
import Image from "next/image";
import Link from "next/link";
import React, { useState } from "react";
import AppButton from "./AppButton";
import ICONS from "@/public/icons";
import COLORS from "@/constants/colors";

type City = CitiesQuery["cities"];

function ListItem({
  title,
  children,
  href,
  onHover,
  ...props
}: React.ComponentPropsWithoutRef<"li"> & {
  href: string;
  onHover?: () => void;
}) {
  return (
    <li
      {...props}
      onMouseEnter={onHover}
      className="bg-transparent transition-colors rounded-sm px-2"
    >
      <NavigationMenuLink asChild>
        <Link href={href}>
          <div className="text-sm leading-none font-medium">{title}</div>
          <p className="text-muted-foreground line-clamp-2 text-sm leading-snug">
            {children}
          </p>
        </Link>
      </NavigationMenuLink>
    </li>
  );
}

export default function Navbar(cities: City) {
  const { docs } = cities;
  const [hoveredImage, setHoveredImage] = useState<string>("/logo.svg");
  return (
    <>
      {/* WEB VIEW */}
      <nav className="w-full container py-4 bg-background sm:flex sm:items-center sm:justify-between hidden">
        <Link href={"/"}>
          <Image
            src="/logo.svg"
            width={130}
            height={40}
            alt="logo"
            className="w-[130px] h-[40px] lg:w-[175px] lg:h-[57px]"
          />
        </Link>
        <div className="flex flex-1 justify-end mt-2">
          <NavigationMenu viewport={false}>
            <NavigationMenuList className="sm:gap-0">
              {/* BARS LIST */}
              <NavigationMenuItem>
                <NavigationMenuTrigger className="text-sm lg:text-base font-montserrat-alternates font-normal hover:bg-transparent focus:bg-transparent data-[state=open]:hover:bg-transparent data-[state=open]:focus:bg-transparent data-[state=open]:bg-transparent">
                  Bars
                </NavigationMenuTrigger>
                <NavigationMenuContent>
                  <ul className="grid gap-2 md:w-[400px] lg:w-[500px] lg:grid-cols-[.75fr_1fr]">
                    {/* Image Preview Section */}
                    <li className="row-span-5">
                      <NavigationMenuLink asChild className="">
                        <div className="flex items-center justify-center w-full h-[300px] rounded-md overflow-hidden">
                          <Image
                            src={hoveredImage}
                            width={175}
                            height={120}
                            alt="City preview"
                            className="rounded-md object-cover transition-all duration-300 w-full h-full"
                            loading="eager"
                            quality={100}
                            priority={true}
                          />
                        </div>
                      </NavigationMenuLink>
                    </li>

                    {/* City Links */}
                    {docs?.map((city) => (
                      <ListItem
                        key={city.id}
                        href={`/${city.name}/bars`}
                        title={city.name}
                        onHover={() =>
                          setHoveredImage(city.image || "/logo.svg")
                        }
                      >
                        {city.heading}
                      </ListItem>
                    ))}
                  </ul>
                </NavigationMenuContent>
              </NavigationMenuItem>

              {/* CLUBS LIST */}
              <NavigationMenuItem>
                <NavigationMenuTrigger className="text-sm lg:text-base font-montserrat-alternates font-normal hover:bg-transparent focus:bg-transparent data-[state=open]:hover:bg-transparent data-[state=open]:focus:bg-transparent data-[state=open]:bg-transparent">
                  Clubs
                </NavigationMenuTrigger>
                <NavigationMenuContent>
                  <ul className="grid gap-2 md:w-[400px] lg:w-[500px] lg:grid-cols-[.75fr_1fr]">
                    {/* Image Preview Section */}
                    <li className="row-span-5">
                      <NavigationMenuLink asChild>
                        <div className="flex items-center justify-center w-full h-[300px] rounded-md overflow-hidden">
                          <Image
                            src={hoveredImage}
                            width={175}
                            height={120}
                            alt="City preview"
                            className="rounded-md object-cover transition-all duration-300 w-full h-full"
                            loading="eager"
                            quality={100}
                            priority={true}
                          />
                        </div>
                      </NavigationMenuLink>
                    </li>

                    {/* City Links */}
                    {docs?.map((city) => (
                      <ListItem
                        key={city.id}
                        href={`/${city.name}/clubs`}
                        title={city.name}
                        onHover={() =>
                          setHoveredImage(city.image || "/logo.svg")
                        }
                      >
                        {city.heading}
                      </ListItem>
                    ))}
                  </ul>
                </NavigationMenuContent>
              </NavigationMenuItem>

              {/* DISABLED EVENT BUTTON (ADD TOOLTIP AS COMING SOON) */}
              <NavigationMenuItem>
                <NavigationMenuLink
                  asChild
                  className="text-sm lg:text-base font-montserrat-alternates font-normal mr-2 xl:mr-0 hover:bg-transparent focus:bg-transparent data-[state=open]:hover:bg-transparent data-[state=open]:focus:bg-transparent data-[state=open]:bg-transparent"
                >
                  <Link href="/">Events</Link>
                </NavigationMenuLink>
              </NavigationMenuItem>

              {/* CONTACT US BUTTON */}
              <NavigationMenuItem>
                <NavigationMenuLink
                  asChild
                  className="text-sm lg:text-base font-montserrat-alternates font-normal mr-2 xl:mr-0 hover:bg-transparent focus:bg-transparent data-[state=open]:hover:bg-transparent data-[state=open]:focus:bg-transparent data-[state=open]:bg-transparent"
                >
                  <Link href="/contact">Contact Us</Link>
                </NavigationMenuLink>
              </NavigationMenuItem>

              {/* DOWNLOAD APP BUTTON */}
              <AppButton
                className="px-4 ml-4 lg:px-7 py-3.5 rounded-full"
                variant="gradient2"
              >
                DOWNLOAD APP
              </AppButton>
            </NavigationMenuList>
          </NavigationMenu>
        </div>
      </nav>
      {/* MOBILE VIEW */}
      <nav className="w-full py-5 px-4 relative bg-background flex flex-row items-center justify-between sm:hidden">
        <Link href={"/"}>
          <Image
            src="/logo.svg"
            width={110}
            height={36}
            alt="logo"
            className="w-[110px] h-[36px]"
          />
        </Link>
        <Sheet modal={false}>
          <SheetTrigger asChild>
            <button>
              <ICONS.NavbarIcon
                width={25}
                height={21}
                color={COLORS.LIGHT.BLACK}
              />
            </button>
          </SheetTrigger>
          <SheetContent className="bg-background z-50">
            <SheetHeader className="bg-background">
              <SheetTitle className="w-full flex items-center justify-center -mt-1 pb-4 border-b border-muted">
                <Image
                  src="/logo.svg"
                  width={90}
                  height={36}
                  alt="logo"
                  className="w-[90px] h-[36px]"
                />
              </SheetTitle>
            </SheetHeader>
            <div className="w-full px-4 flex flex-col justify-center gap-10">
              {["Bars", "Clubs", "Events", "Contact us"].map((link, idx) => (
                <Link
                  className="font-montserrat-alternates text-xl font-normal text-black "
                  key={idx}
                  href={"/"}
                >
                  {link}
                </Link>
              ))}
              <AppButton className="py-2.5 rounded-full" variant="gradient2">
                DOWNLOAD APP
              </AppButton>
            </div>
          </SheetContent>
        </Sheet>
      </nav>
    </>
  );
}
