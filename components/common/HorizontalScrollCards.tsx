"use client";

import React, { useState, useRef, useEffect } from "react";

import { cn } from "@/lib/utils";
import ICONS from "@/public/icons";
import COLORS from "@/constants/colors";

const HorizontalScrollCards = ({ children }: { children: React.ReactNode }) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);
  const [showLeftArrow, setShowLeftArrow] = useState(false);
  const [showRightArrow, setShowRightArrow] = useState(true);
  const [velocity, setVelocity] = useState(0);
  const [lastX, setLastX] = useState(0);
  const [lastTime, setLastTime] = useState(0);
  const animationRef = useRef<number | null>(null);

  const updateArrowVisibility = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } =
        scrollContainerRef.current;
      setShowLeftArrow(scrollLeft > 0);
      setShowRightArrow(scrollLeft < scrollWidth - clientWidth - 10);
    }
  };

  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>): void => {
    if (scrollContainerRef.current) {
      // Cancel any ongoing inertia animation
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }

      setIsDragging(true);
      const containerRect: DOMRect =
        scrollContainerRef.current.getBoundingClientRect();
      setStartX(e.clientX - containerRect.left);
      setScrollLeft(scrollContainerRef.current.scrollLeft);
      setLastX(e.clientX);
      setLastTime(Date.now());
      setVelocity(0);

      // Prevent text selection
      e.preventDefault();
    }
  };

  const handleMouseMove = (e: MouseEvent): void => {
    if (!isDragging || !scrollContainerRef.current) return;
    e.preventDefault();

    const currentTime: number = Date.now();
    const currentX: number = e.clientX;
    const timeDelta: number = currentTime - lastTime;

    // Calculate velocity for inertia (pixels per millisecond)
    if (timeDelta > 0) {
      const deltaX: number = currentX - lastX;
      const newVelocity: number = deltaX / timeDelta;
      setVelocity(newVelocity);
    }

    // Apply the drag movement
    const containerRect: DOMRect =
      scrollContainerRef.current.getBoundingClientRect();
    const x: number = e.clientX - containerRect.left;
    const walk: number = x - startX;
    scrollContainerRef.current.scrollLeft = scrollLeft - walk;

    setLastX(currentX);
    setLastTime(currentTime);
  };

  const handleMouseUp = (): void => {
    setIsDragging(false);
    if (Math.abs(velocity) > 0.05) {
      applyInertia(velocity);
    }
  };

  const applyInertia = (currentVelocity: number): void => {
    if (!scrollContainerRef.current || Math.abs(currentVelocity) < 0.05) {
      animationRef.current = null;
      return;
    }

    const friction = 0.92;
    const newVelocity = currentVelocity * friction;

    // Convert velocity to scroll distance (multiply by frame time approximation)
    const scrollDelta = newVelocity * 16;
    scrollContainerRef.current.scrollLeft -= scrollDelta;

    animationRef.current = requestAnimationFrame(() =>
      applyInertia(newVelocity)
    );
  };

  const scrollToDirection = (direction: "left" | "right"): void => {
    if (scrollContainerRef.current) {
      const scrollAmount = 320; // Width of one card plus gap
      const newScrollLeft =
        direction === "left"
          ? scrollContainerRef.current.scrollLeft - scrollAmount
          : scrollContainerRef.current.scrollLeft + scrollAmount;

      scrollContainerRef.current.scrollTo({
        left: newScrollLeft,
        behavior: "smooth",
      });
    }
  };

  // Effect to handle global mouse events during dragging
  useEffect(() => {
    if (isDragging) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);

      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };
    }
  }, [isDragging, startX, scrollLeft, lastX, lastTime, velocity]);

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener("scroll", updateArrowVisibility);
      updateArrowVisibility(); // Initial check

      return () => {
        container.removeEventListener("scroll", updateArrowVisibility);
        // Clean up animation frame on unmount
        if (animationRef.current) {
          cancelAnimationFrame(animationRef.current);
        }
      };
    }
  }, []);

  return (
    <div className="relative w-full">
      <div className="relative">
        {/* Left Arrow */}
        {showLeftArrow && (
          <button
            onClick={() => scrollToDirection("left")}
            className="absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-white rounded-full p-2 shadow-lg hover:shadow-xl transition-shadow border"
            style={{ marginLeft: "-12px" }}
          >
            <ICONS.ChevronLeftIcon
              width={20}
              height={20}
              color={COLORS.LIGHT.TEXT}
            />
          </button>
        )}

        {/* Right Arrow */}
        {showRightArrow && (
          <button
            onClick={() => scrollToDirection("right")}
            className="absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-white rounded-full p-2 shadow-lg hover:shadow-xl transition-shadow border"
            style={{ marginRight: "-12px" }}
          >
            <ICONS.ChevronRightIcon
              width={20}
              height={20}
              color={COLORS.LIGHT.TEXT}
            />
          </button>
        )}

        {/* Scrollable Container */}
        <div
          ref={scrollContainerRef}
          className={cn(
            "flex gap-5 md:gap-6 overflow-x-auto scrollbar-hide select-none py-4",
            isDragging ? "cursor-grabbing" : "cursor-pointer"
          )}
          style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}
          onMouseDown={handleMouseDown}
        >
          {children}
        </div>
      </div>
    </div>
  );
};

export default HorizontalScrollCards;
