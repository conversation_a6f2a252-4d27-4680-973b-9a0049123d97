"use client";

import React from "react";
import RatingStars from "../common/RatingStars";
import Image from "next/image";
import COLORS from "@/constants/colors";
import ICONS from "@/public/icons";
import { cn } from "@/lib/utils";
import { BarsQuery, ClubsQuery } from "@/generated/graphql";

type BarOrClub =
  | BarsQuery["bars"]["docs"][number]
  | ClubsQuery["clubs"]["docs"][number];

type PopularCardProps = {
  cardType: "bar" | "club";
  data: BarOrClub;
};

function PopularCard({
  cardType,
  data,
  ...props
}: PopularCardProps & React.HTMLAttributes<HTMLDivElement>) {
  if (!data?.slug || !data?.name || !data?.address?.address) return null;
  const name = data?.name;
  const address = data?.address?.address;
  const rating = data?.rating;
  const logo = data?.logo;
  const coverimage = data?.coverImage;
  const tags = data?.categories?.map((c) => c.name);
  const currency = data?.menu?.currency;
  const cost = "5.00";
  return (
    <div
      className="w-[298px] min-w-[298px] overflow-hidden rounded-[18px] shadow-lg shadow-neutral-300/20 border border-neutral-200/30"
      {...props}
    >
      <div className="relative w-full h-[198px]">
        <Image
          src={coverimage || "/svg/cheers.svg"}
          fill
          alt="bar"
          className="object-cover"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 298px"
        />
        <div className="absolute w-full px-4.5 py-5">
          <RatingStars className="px-2" ratingValue={rating} />
        </div>
      </div>
      <div
        className={cn(
          "h-full bg-gradient-to-br from-muted-primary from-50% to-primary relative py-5 px-4",
          cardType === "club" &&
            "bg-gradient-to-r from-text from-20% to-secondary to-80% "
        )}
      >
        <div className="flex flex-row items-center absolute top-5 right-4.5 gap-1">
          <ICONS.BeerJugIcon
            width={20}
            height={20}
            color={cardType === "bar" ? COLORS.LIGHT.TEXT : COLORS.LIGHT.WHITE}
          />
          <p
            className={cn(
              "font-montserrat text-base font-semibold text-text mt-1",
              cardType === "club" && "text-white"
            )}
          >
            {currency} {cost}
          </p>
        </div>
        <div className="absolute w-[69px] h-[68px] rounded-[6px] border-2 border-ring overflow-hidden left-7 -top-8">
          <Image
            src={logo || "/svg/tempLogo.svg"}
            alt="barlogo"
            fill
            sizes="69px"
          />
        </div>
        <div className="mt-6 ml-4">
          <div
            className={cn(
              "font-montserrat-alternates font-semibold text-base text-text",
              cardType === "club" && "text-white"
            )}
          >
            {name}
          </div>
          <div className="flex flex-row items-center gap-2 mt-2">
            <div className="bg-white min-w-6 min-h-6 rounded-full flex items-center justify-center">
              <ICONS.PinIcon
                width={15}
                height={15}
                color={COLORS.LIGHT.BLACK}
              />
            </div>
            <div
              className={cn(
                "text-[11px] text-text font-montserrat-alternates font-medium truncate",
                cardType === "club" && "text-white"
              )}
            >
              {address}
            </div>
          </div>
          <div className="flex flex-row items-start gap-2 mt-3">
            <div className="bg-white min-w-6 min-h-6 rounded-full flex items-center justify-center">
              {cardType === "bar" && (
                <ICONS.BeerJugIcon
                  width={15}
                  height={15}
                  color={COLORS.LIGHT.BLACK}
                />
              )}
              {cardType === "club" && (
                <ICONS.MusicIcon
                  width={15}
                  height={15}
                  color={COLORS.LIGHT.BLACK}
                />
              )}
            </div>
            <div className="flex flex-row items-center gap-2 flex-wrap">
              {tags?.map((tag, idx) => (
                <div
                  key={idx}
                  className="bg-white rounded-sm flex items-center justify-center px-1 py-1 text-[11px] font-montserrat-alternates font-medium"
                >
                  {tag}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default React.memo(PopularCard);
